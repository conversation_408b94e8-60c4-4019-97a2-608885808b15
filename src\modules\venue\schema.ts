import { z } from '@hono/zod-openapi';

// 场馆实体 Schema（多语言支持）
export const venueSchema = z.object({
  id: z.string().openapi({ example: 'tokyo-big-sight' }),

  /* ---------- 场馆名称 ---------- */
  name_en: z.string().openapi({ example: 'Tokyo Big Sight' }),
  name_ja: z.string().openapi({ example: '東京ビッグサイト' }),
  name_zh: z.string().openapi({ example: '东京 Big Sight' }),

  /* ---------- 场馆地址 ---------- */
  address_en: z.string().nullable().optional(),
  address_ja: z.string().nullable().optional(),
  address_zh: z.string().nullable().optional(),

  /* ---------- 地理位置 ---------- */
  lat: z.number().openapi({ example: 35.6298 }),
  lng: z.number().openapi({ example: 139.793 }),

  /* ---------- 基本信息 ---------- */
  capacity: z.number().nullable().optional(),
  website_url: z.string().nullable().optional(),
  phone: z.string().nullable().optional(),

  /* ---------- 多语言描述 ---------- */
  description_en: z.string().nullable().optional(),
  description_ja: z.string().nullable().optional(),
  description_zh: z.string().nullable().optional(),

  /* ---------- JSON扩展信息 ---------- */
  facilities: z.string().nullable().optional(), // JSON string
  transportation: z.string().nullable().optional(), // JSON string
  parking_info: z.string().nullable().optional(), // JSON string

  /* ---------- 时间戳 ---------- */
  created_at: z.string().optional(),
  updated_at: z.string().optional(),
});

export type Venue = z.infer<typeof venueSchema>;

// 本地化的场馆信息（API返回格式）
export const localizedVenueSchema = z.object({
  id: z.string().openapi({ example: 'tokyo-big-sight' }),
  name: z.string().openapi({ example: 'Tokyo Big Sight' }),
  address: z.string().nullable().optional(),
  description: z.string().nullable().optional(),
  lat: z.number().openapi({ example: 35.6298 }),
  lng: z.number().openapi({ example: 139.793 }),
  capacity: z.number().nullable().optional(),
  website_url: z.string().nullable().optional(),
  phone: z.string().nullable().optional(),
  facilities: z.string().nullable().optional(),
  transportation: z.string().nullable().optional(),
  parking_info: z.string().nullable().optional(),
  created_at: z.string().optional(),
  updated_at: z.string().optional(),
});

export type LocalizedVenue = z.infer<typeof localizedVenueSchema>;

// 创建请求体（不包含 id/时间戳，由后端生成）
export const venueCreateRequest = venueSchema.omit({
  id: true,
  created_at: true,
  updated_at: true,
});

export type VenueCreateInput = z.infer<typeof venueCreateRequest>;

// 更新请求体（全部字段可选）
export const venueUpdateRequest = venueCreateRequest.partial();

export type VenueUpdateInput = z.infer<typeof venueUpdateRequest>;

/**
 * 数据本地化工具函数
 * 将完整的venue数据转换为本地化数据
 */
export function localizeVenue(
  venue: Venue,
  locale: 'en' | 'ja' | 'zh'
): LocalizedVenue {
  return {
    id: venue.id,
    name: venue[`name_${locale}`] || venue.name_en,
    address: venue[`address_${locale}`] || venue.address_en || undefined,
    description:
      venue[`description_${locale}`] || venue.description_en || undefined,
    lat: venue.lat,
    lng: venue.lng,
    capacity: venue.capacity,
    website_url: venue.website_url,
    phone: venue.phone,
    facilities: venue.facilities,
    transportation: venue.transportation,
    parking_info: venue.parking_info,
    created_at: venue.created_at,
    updated_at: venue.updated_at,
  };
}

/**
 * 场馆搜索参数
 */
export const venueSearchParams = z.object({
  page: z.string().optional().openapi({ example: '1' }),
  pageSize: z.string().optional().openapi({ example: '50' }),
  keyword: z.string().optional().openapi({ example: 'Big Sight' }),
  city: z.string().optional().openapi({ example: 'Tokyo' }),
  capacity_min: z.string().optional().openapi({ example: '1000' }),
  capacity_max: z.string().optional().openapi({ example: '10000' }),
  has_parking: z.string().optional().openapi({ example: 'true' }),
  has_wifi: z.string().optional().openapi({ example: 'true' }),
});

export type VenueSearchParams = z.infer<typeof venueSearchParams>;

// 场馆输入Schema（用于创建/更新）
export const venueInputSchema = z.object({
  id: z.string().optional(),

  // 多语言名称（必填）
  name_en: z.string().min(1, '英文名称必填'),
  name_ja: z.string().min(1, '日文名称必填'),
  name_zh: z.string().min(1, '中文名称必填'),

  // 多语言地址（可选）
  address_en: z.string().optional(),
  address_ja: z.string().optional(),
  address_zh: z.string().optional(),

  // 地理位置（必填）
  lat: z.number().min(-90).max(90, '纬度必须在 -90 到 90 之间'),
  lng: z.number().min(-180).max(180, '经度必须在 -180 到 180 之间'),

  // 基本信息（可选）
  capacity: z.number().min(0).optional(),
  website_url: z.string().url('请输入有效的URL').optional().or(z.literal('')),
  phone: z.string().optional(),

  // 多语言描述（可选）
  description_en: z.string().optional(),
  description_ja: z.string().optional(),
  description_zh: z.string().optional(),

  // JSON扩展信息（可选）
  facilities: z.string().optional(),
  transportation: z.string().optional(),
  parking_info: z.string().optional(),
});

export type VenueInput = z.infer<typeof venueInputSchema>;

// 场馆设施信息类型定义
export interface VenueFacilities {
  wifi: boolean;
  parking: boolean;
  restaurant: boolean;
  atm: boolean;
  accessibility: boolean;
  air_conditioning: boolean;
  restrooms: number;
  elevators: number;
  escalators: number;
  baby_care_room: boolean;
  smoking_area: boolean;
  lockers: boolean;
}

// 场馆交通信息类型定义
export interface VenueTransportation {
  nearest_stations: Array<{
    name: string;
    line: string;
    walking_minutes: number;
    distance_meters: number;
  }>;
  bus_routes: Array<{
    route: string;
    stop_name: string;
    walking_minutes: number;
  }>;
  taxi_info: {
    estimated_fare_from_station: string;
    estimated_time_minutes: number;
  };
}

// 场馆停车信息类型定义
export interface VenueParkingInfo {
  available: boolean;
  capacity: number;
  hourly_rate: number;
  daily_rate: number;
  free_hours: number;
  payment_methods: string[];
  restrictions: string[];
}

// 后端数据转换工具函数类型定义
export interface VenueLocalizationUtils {
  /**
   * 将完整的venue数据转换为本地化数据
   * @param venue 完整的venue数据
   * @param locale 目标语言
   * @returns 本地化的venue数据
   */
  localizeVenue: (venue: Venue, locale: 'en' | 'ja' | 'zh') => LocalizedVenue;

  /**
   * 解析JSON字段为对象
   * @param jsonString JSON字符串
   * @returns 解析后的对象
   */
  parseVenueExtensions: (venue: LocalizedVenue) => {
    facilities?: VenueFacilities;
    transportation?: VenueTransportation;
    parking_info?: VenueParkingInfo;
  };
}
