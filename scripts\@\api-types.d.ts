/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */

export interface paths {
  '/auth/register': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /** Register a new user */
    post: operations['post_auth_register'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/auth/logout': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /** 退出登录 */
    post: operations['post_auth_logout'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/auth/me': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** 获取当前用户信息 */
    get: operations['get_auth_me'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/auth/refresh': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /** 刷新会话 */
    post: operations['post_auth_refresh'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/events': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** 展会列表（公开） */
    get: operations['get_events'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/events/{id}': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** 展会详情 */
    get: operations['get_events_id'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/events/{id}/circles': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** 展会社团列表 */
    get: operations['get_events_id_circles'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/events/{id}/appearances': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** 展会参展记录 */
    get: operations['get_events_id_appearances'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/circles': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** 社团列表（公开） */
    get: operations['get_circles'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/circles/{id}': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** 社团详情（按 ID） */
    get: operations['get_circles_id'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/circles/{id}/appearances': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** 社团参展历史 */
    get: operations['get_circles_id_appearances'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/circles/{circleId}/bookmark': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /** 切换收藏状态 */
    post: operations['post_circles_circleId_bookmark'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/artists': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** 作者列表 */
    get: operations['get_artists'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/artists/{id}': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** 作者详情 */
    get: operations['get_artists_id'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/appearances': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * 参展记录查询
     * @deprecated
     */
    get: operations['get_appearances'];
    put?: never;
    /** 创建参展记录 */
    post: operations['post_appearances'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/appearances/{id}': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** 参展记录详情 */
    get: operations['get_appearances_id'];
    put?: never;
    post?: never;
    /** 删除参展记录 */
    delete: operations['delete_appearances_id'];
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/search': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** 搜索内容 */
    get: operations['get_search'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/feed': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** 获取 Feed 流 */
    get: operations['get_feed'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/admin/events': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** 展会列表（分页 & 搜索） */
    get: operations['get_admin_events'];
    put?: never;
    /** 创建展会 */
    post: operations['post_admin_events'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/admin/events/{id}': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** 展会详情 */
    get: operations['get_admin_events_id'];
    put?: never;
    post?: never;
    /** 删除展会 */
    delete: operations['delete_admin_events_id'];
    options?: never;
    head?: never;
    /** 更新展会 */
    patch: operations['patch_admin_events_id'];
    trace?: never;
  };
  '/admin/circles': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** 社团列表 */
    get: operations['get_admin_circles'];
    put?: never;
    /** 创建社团 */
    post: operations['post_admin_circles'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/admin/circles/{id}': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** 社团详情 */
    get: operations['get_admin_circles_id'];
    /** 更新社团 */
    put: operations['put_admin_circles_id'];
    post?: never;
    /** 删除社团 */
    delete: operations['delete_admin_circles_id'];
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/admin/venues': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** 场馆列表（管理员） */
    get: operations['get_admin_venues'];
    put?: never;
    /** 创建场馆 */
    post: operations['post_admin_venues'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/admin/venues/{id}': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** 场馆详情（管理员） */
    get: operations['get_admin_venues_id'];
    /** 更新场馆 */
    put: operations['put_admin_venues_id'];
    post?: never;
    /** 删除场馆 */
    delete: operations['delete_admin_venues_id'];
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/admin/images/upload': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /** 上传图片 */
    post: operations['post_admin_images_upload'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/admin/images': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    post?: never;
    /** 删除图片 */
    delete: operations['delete_admin_images'];
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/admin/images/{id}': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** 获取图片详细信息 */
    get: operations['get_admin_images_id'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/admin/users': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** 用户列表 */
    get: operations['get_admin_users'];
    put?: never;
    /** 创建用户 */
    post: operations['post_admin_users'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/admin/users/{id}': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** 用户详情 */
    get: operations['get_admin_users_id'];
    /** 更新用户 */
    put: operations['put_admin_users_id'];
    post?: never;
    /** 删除用户 */
    delete: operations['delete_admin_users_id'];
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/admin/logs': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** 查询操作日志 */
    get: operations['get_admin_logs'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/admin/stats': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** 后台统计数据 */
    get: operations['get_admin_stats'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
}
export type webhooks = Record<string, never>;
export interface components {
  schemas: {
    /**
     * @description 业务错误码
     * @enum {integer}
     */
    ErrorCodes:
      | 10001
      | 10002
      | 10003
      | 20001
      | 20002
      | 30001
      | 30002
      | 40001
      | 40002
      | 40003
      | 50001
      | 50002
      | 60001
      | 60002
      | 60003;
    ErrorResponse: {
      code: components['schemas']['ErrorCodes'];
      message: string;
      detail?: unknown;
      requestId: string;
    };
    AuthCredentials: {
      /** @example alice */
      username: string;
      /** @example secret123 */
      password: string;
    };
    PaginatedResult: {
      /** @example 120 */
      total: number;
      /** @example 1 */
      page: number;
      /** @example 20 */
      pageSize: number;
      items: {
        /** @example uuid-123 */
        id: string;
        /** @example Reitaisai 22 */
        name_en: string;
        /** @example 第二十二回博麗神社例大祭 */
        name_ja: string;
        /** @example 第二十二回博丽神社例大祭 */
        name_zh: string;
        /** @example May 3, 2025 (Sat) 10:30 – 15:30 */
        date_en: string;
        /** @example 2025年5月3日(土・祝) 10:30 – 15:30 */
        date_ja: string;
        /** @example 2025年5月3日(周六) 10:30 – 15:30 */
        date_zh: string;
        /** @example 20250503 */
        date_sort?: number;
        image_url?: string | null;
        /** @example tokyo-big-sight */
        venue_id: string;
        url?: string | null;
        created_at?: string;
        updated_at?: string;
      }[];
    };
    SuccessResponse: {
      message: string;
      data?: unknown;
    };
  };
  responses: never;
  parameters: never;
  requestBodies: never;
  headers: never;
  pathItems: never;
}
export type $defs = Record<string, never>;
export interface operations {
  post_auth_register: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: {
      content: {
        'application/json': components['schemas']['AuthCredentials'];
      };
    };
    responses: {
      /** @description User created */
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            /** @example uuid-123 */
            id: string;
            /** @example alice */
            username: string;
          };
        };
      };
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': components['schemas']['ErrorResponse'];
        };
      };
      /** @description Validation Error */
      422: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': components['schemas']['ErrorResponse'];
        };
      };
    };
  };
  post_auth_logout: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description 退出成功 */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            /** @example 已退出登录 */
            message: string;
          };
        };
      };
    };
  };
  get_auth_me: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description 用户信息 */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            /** @example uuid-123 */
            id: string;
            /** @example alice */
            username: string;
            /** @example viewer */
            role: string;
          };
        };
      };
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  post_auth_refresh: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description 会话刷新成功 */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            /** @example 0 */
            code: number;
            /** @example 会话刷新成功 */
            message: string;
            data: {
              /** @example uuid-123 */
              id: string;
              /** @example alice */
              username: string;
              /** @example viewer */
              role: string;
            };
          };
        };
      };
      /** @description Unauthorized */
      401: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  get_events: {
    parameters: {
      query?: {
        page?: string;
        pageSize?: string;
        keyword?: string;
        date_from?: string;
        date_to?: string;
      };
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description 展会列表 */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': components['schemas']['PaginatedResult'];
        };
      };
    };
  };
  get_events_id: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description 展会详情 */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            /** @example uuid-123 */
            id: string;
            /** @example Reitaisai 22 */
            name_en: string;
            /** @example 第二十二回博麗神社例大祭 */
            name_ja: string;
            /** @example 第二十二回博丽神社例大祭 */
            name_zh: string;
            /** @example May 3, 2025 (Sat) 10:30 – 15:30 */
            date_en: string;
            /** @example 2025年5月3日(土・祝) 10:30 – 15:30 */
            date_ja: string;
            /** @example 2025年5月3日(周六) 10:30 – 15:30 */
            date_zh: string;
            /** @example 20250503 */
            date_sort?: number;
            image_url?: string | null;
            /** @example tokyo-big-sight */
            venue_id: string;
            url?: string | null;
            created_at?: string;
            updated_at?: string;
          };
        };
      };
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  get_events_id_circles: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description 社团列表 */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            /** @example uuid-123 */
            id: string;
            /** @example Reitaisai 22 */
            name_en: string;
            /** @example 第二十二回博麗神社例大祭 */
            name_ja: string;
            /** @example 第二十二回博丽神社例大祭 */
            name_zh: string;
            /** @example May 3, 2025 (Sat) 10:30 – 15:30 */
            date_en: string;
            /** @example 2025年5月3日(土・祝) 10:30 – 15:30 */
            date_ja: string;
            /** @example 2025年5月3日(周六) 10:30 – 15:30 */
            date_zh: string;
            /** @example 20250503 */
            date_sort?: number;
            image_url?: string | null;
            /** @example tokyo-big-sight */
            venue_id: string;
            url?: string | null;
            created_at?: string;
            updated_at?: string;
            /** @example あ01a */
            booth_id?: string | null;
          }[];
        };
      };
    };
  };
  get_events_id_appearances: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description 参展记录分页 */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': components['schemas']['PaginatedResult'] & {
            /** @example 120 */
            total?: number;
            /** @example 1 */
            page?: number;
            /** @example 20 */
            pageSize?: number;
            items?: unknown[];
          };
        };
      };
    };
  };
  get_circles: {
    parameters: {
      query?: {
        page?: string;
        pageSize?: string;
        search?: string;
        category?: string;
      };
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description 社团列表 */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': components['schemas']['PaginatedResult'] & {
            /** @example 120 */
            total?: number;
            /** @example 1 */
            page?: number;
            /** @example 20 */
            pageSize?: number;
            items?: {
              /** @example uuid-123 */
              id: string;
              /** @example 東方愛好会 */
              name: string;
              /** @example comic */
              category?: string | null;
              /** @example {"author":"Alice","twitter_url":"https://twitter.com/example"} */
              urls?: string | null;
              /** @example 2024-01-01T00:00:00Z */
              created_at?: string;
              /** @example 2024-01-01T00:00:00Z */
              updated_at?: string;
            }[];
          };
        };
      };
    };
  };
  get_circles_id: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description 社团详情 */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            /** @example uuid-123 */
            id: string;
            /** @example 東方愛好会 */
            name: string;
            /** @example comic */
            category?: string | null;
            /** @example {"author":"Alice","twitter_url":"https://twitter.com/example"} */
            urls?: string | null;
            /** @example 2024-01-01T00:00:00Z */
            created_at?: string;
            /** @example 2024-01-01T00:00:00Z */
            updated_at?: string;
          };
        };
      };
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  get_circles_id_appearances: {
    parameters: {
      query?: {
        page?: string;
        pageSize?: string;
      };
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description 参展记录分页 */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': components['schemas']['PaginatedResult'] & {
            /** @example 120 */
            total?: number;
            /** @example 1 */
            page?: number;
            /** @example 20 */
            pageSize?: number;
            items?: unknown[];
          };
        };
      };
    };
  };
  post_circles_circleId_bookmark: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        circleId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description 收藏状态已切换 */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': components['schemas']['SuccessResponse'] & {
            data: {
              isBookmarked: boolean;
            };
          };
        };
      };
      /** @description 未登录 */
      401: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': components['schemas']['ErrorResponse'];
        };
      };
    };
  };
  get_artists: {
    parameters: {
      query?: {
        page?: string;
        pageSize?: string;
      };
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description 作者列表 */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': components['schemas']['PaginatedResult'] & {
            /** @example 120 */
            total?: number;
            /** @example 1 */
            page?: number;
            /** @example 20 */
            pageSize?: number;
            items?: {
              /** @example uuid-123 */
              id: string;
              /** @example Alice */
              name: string;
              urls?: string | null;
              /** @example 2024-01-01T00:00:00Z */
              created_at: string;
              /** @example 2024-01-01T00:00:00Z */
              updated_at: string;
              description?: string | null;
            }[];
          };
        };
      };
    };
  };
  get_artists_id: {
    parameters: {
      query?: {
        lang?: string;
      };
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description 作者详情 */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            /** @example uuid-123 */
            id: string;
            /** @example Alice */
            name: string;
            urls?: string | null;
            /** @example 2024-01-01T00:00:00Z */
            created_at: string;
            /** @example 2024-01-01T00:00:00Z */
            updated_at: string;
            description?: string | null;
          };
        };
      };
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  get_appearances: {
    parameters: {
      query?: {
        circle_id?: string;
        event_id?: string;
        page?: string;
        pageSize?: string;
      };
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description 参展记录 */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': components['schemas']['PaginatedResult'] & {
            /** @example 120 */
            total?: number;
            /** @example 1 */
            page?: number;
            /** @example 20 */
            pageSize?: number;
            items?: {
              /** @example uuid-123 */
              id: string;
              /** @example circle-uuid */
              circle_id: string;
              /** @example event-uuid */
              event_id: string;
              /** @example artist-uuid */
              artist_id?: string | null;
              /** @example A01a */
              booth_id: string;
              /** @example /2025/05/03/A1.jpg */
              path?: string | null;
              /** @example 2024-01-01T00:00:00Z */
              created_at: string;
              /** @example 2024-01-01T00:00:00Z */
              updated_at: string;
            }[];
          };
        };
      };
    };
  };
  post_appearances: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: {
      content: {
        'application/json': {
          /** @example uuid-123 */
          id?: string;
          /** @example circle-uuid */
          circle_id?: string;
          /** @example event-uuid */
          event_id?: string;
          /** @example artist-uuid */
          artist_id?: string | null;
          /** @example A01a */
          booth_id?: string;
          /** @example /2025/05/03/A1.jpg */
          path?: string | null;
        };
      };
    };
    responses: {
      /** @description success */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            success: boolean;
          };
        };
      };
    };
  };
  get_appearances_id: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description 详情 */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            /** @example uuid-123 */
            id: string;
            /** @example circle-uuid */
            circle_id: string;
            /** @example event-uuid */
            event_id: string;
            /** @example artist-uuid */
            artist_id?: string | null;
            /** @example A01a */
            booth_id: string;
            /** @example /2025/05/03/A1.jpg */
            path?: string | null;
            /** @example 2024-01-01T00:00:00Z */
            created_at: string;
            /** @example 2024-01-01T00:00:00Z */
            updated_at: string;
          };
        };
      };
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  delete_appearances_id: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description success */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            success: boolean;
          };
        };
      };
    };
  };
  get_search: {
    parameters: {
      query: {
        q: string;
        type?: 'all' | 'events' | 'circles';
        page?: string;
        limit?: string;
      };
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description 搜索结果 */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            /** @example true */
            success: boolean;
            data: {
              /**
               * @description 结果类型
               * @example event
               * @enum {string}
               */
              type: 'event' | 'circle';
              /**
               * @description 资源ID
               * @example 550e8400-e29b-41d4-a716-446655440000
               */
              id: string;
              /**
               * @description 名称
               * @example Comiket 103
               */
              name: string;
              /**
               * @description 描述
               * @example 世界最大的同人志即卖会
               */
              description: string | null;
              /**
               * @description 场馆名称（仅事件类型）
               * @example 东京国际展示场
               */
              venue_name?: string | null;
              /**
               * @description 开始时间（仅事件类型）
               * @example 2024-12-30T10:00:00Z
               */
              start_date?: string | null;
              /**
               * @description 图片URL
               * @example https://example.com/comiket103.jpg
               */
              image_url?: string | null;
              /**
               * @description 搜索相关性评分
               * @example 0.8567
               */
              rank: number;
            }[];
            /**
             * @description 响应语言
             * @example zh
             */
            locale: string;
            /**
             * @description 响应时间戳
             * @example 2024-01-15T10:30:00.000Z
             */
            timestamp: string;
            meta: {
              /**
               * @description 总结果数
               * @example 15
               */
              total: number;
              /**
               * @description 搜索关键词
               * @example Comiket
               */
              query: string;
              /**
               * @description 搜索类型
               * @example events
               */
              type: string;
            };
          };
        };
      };
      /** @description 请求参数错误 */
      400: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  get_feed: {
    parameters: {
      query?: {
        page?: string;
        limit?: string;
        type?: 'all' | 'events' | 'circles';
      };
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description Feed 流数据 */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            /** @example true */
            success: boolean;
            data: {
              /**
               * @description Feed项ID
               * @example feed-001
               */
              id: string;
              /**
               * @description 内容类型
               * @example event
               * @enum {string}
               */
              type: 'event' | 'circle';
              content: {
                /**
                 * @description 资源ID
                 * @example 550e8400-e29b-41d4-a716-446655440000
                 */
                id: string;
                /**
                 * @description 名称
                 * @example Comiket 103
                 */
                name: string;
                /**
                 * @description 描述
                 * @example 世界最大的同人志即卖会
                 */
                description: string | null;
                /**
                 * @description 开始时间（仅事件类型）
                 * @example 2024-12-30T10:00:00Z
                 */
                start_date?: string | null;
                /**
                 * @description 图片URL
                 * @example https://example.com/comiket103.jpg
                 */
                image_url?: string | null;
              };
              /**
               * @description 创建时间
               * @example 2024-01-15T08:00:00Z
               */
              created_at: string;
            }[];
            /**
             * @description 响应语言
             * @example zh
             */
            locale: string;
            /**
             * @description 响应时间戳
             * @example 2024-01-15T10:30:00.000Z
             */
            timestamp: string;
            meta: {
              /**
               * @description 总数量
               * @example 200
               */
              total: number;
              /**
               * @description 当前页码
               * @example 1
               */
              page: number;
              /**
               * @description 每页数量
               * @example 20
               */
              limit: number;
              /**
               * @description 是否有更多数据
               * @example true
               */
              hasMore: boolean;
            };
          };
        };
      };
      /** @description 请求参数错误 */
      400: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  get_admin_events: {
    parameters: {
      query?: {
        page?: string;
        pageSize?: string;
        keyword?: string;
        date_from?: string;
        date_to?: string;
      };
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description 分页列表 */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': components['schemas']['PaginatedResult'];
        };
      };
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': components['schemas']['ErrorResponse'];
        };
      };
    };
  };
  post_admin_events: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: {
      content: {
        'application/json': {
          /** @example Reitaisai 22 */
          name_en: string;
          /** @example 第二十二回博麗神社例大祭 */
          name_ja: string;
          /** @example 第二十二回博丽神社例大祭 */
          name_zh: string;
          /** @example May 3, 2025 (Sat) 10:30 – 15:30 */
          date_en: string;
          /** @example 2025年5月3日(土・祝) 10:30 – 15:30 */
          date_ja: string;
          /** @example 2025年5月3日(周六) 10:30 – 15:30 */
          date_zh: string;
          /** @example 20250503 */
          date_sort?: number;
          image_url?: string | null;
          /** @example tokyo-big-sight */
          venue_id: string;
          url?: string | null;
        };
      };
    };
    responses: {
      /** @description 展会创建成功 */
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': components['schemas']['SuccessResponse'];
        };
      };
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': components['schemas']['ErrorResponse'];
        };
      };
      /** @description Validation Error */
      422: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  get_admin_events_id: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description 展会详情 */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            /** @example uuid-123 */
            id: string;
            /** @example Reitaisai 22 */
            name_en: string;
            /** @example 第二十二回博麗神社例大祭 */
            name_ja: string;
            /** @example 第二十二回博丽神社例大祭 */
            name_zh: string;
            /** @example May 3, 2025 (Sat) 10:30 – 15:30 */
            date_en: string;
            /** @example 2025年5月3日(土・祝) 10:30 – 15:30 */
            date_ja: string;
            /** @example 2025年5月3日(周六) 10:30 – 15:30 */
            date_zh: string;
            /** @example 20250503 */
            date_sort?: number;
            image_url?: string | null;
            /** @example tokyo-big-sight */
            venue_id: string;
            url?: string | null;
            created_at?: string;
            updated_at?: string;
          };
        };
      };
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': components['schemas']['ErrorResponse'];
        };
      };
    };
  };
  delete_admin_events_id: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description 展会已删除 */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            message: string;
          };
        };
      };
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': components['schemas']['ErrorResponse'];
        };
      };
    };
  };
  patch_admin_events_id: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        'application/json': {
          /** @example Reitaisai 22 */
          name_en?: string;
          /** @example 第二十二回博麗神社例大祭 */
          name_ja?: string;
          /** @example 第二十二回博丽神社例大祭 */
          name_zh?: string;
          /** @example May 3, 2025 (Sat) 10:30 – 15:30 */
          date_en?: string;
          /** @example 2025年5月3日(土・祝) 10:30 – 15:30 */
          date_ja?: string;
          /** @example 2025年5月3日(周六) 10:30 – 15:30 */
          date_zh?: string;
          /** @example 20250503 */
          date_sort?: number;
          image_url?: string | null;
          /** @example tokyo-big-sight */
          venue_id?: string;
          url?: string | null;
        };
      };
    };
    responses: {
      /** @description 展会已保存 */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': components['schemas']['SuccessResponse'];
        };
      };
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': components['schemas']['ErrorResponse'];
        };
      };
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': components['schemas']['ErrorResponse'];
        };
      };
      /** @description Validation Error */
      422: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  get_admin_circles: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description 社团列表 */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': components['schemas']['PaginatedResult'] & {
            /** @example 120 */
            total?: number;
            /** @example 1 */
            page?: number;
            /** @example 20 */
            pageSize?: number;
            items?: {
              /** @example uuid-123 */
              id: string;
              /** @example 東方愛好会 */
              name: string;
              /** @example comic */
              category?: string | null;
              /** @example {"author":"Alice","twitter_url":"https://twitter.com/example"} */
              urls?: string | null;
              /** @example 2024-01-01T00:00:00Z */
              created_at?: string;
              /** @example 2024-01-01T00:00:00Z */
              updated_at?: string;
            }[];
          };
        };
      };
    };
  };
  post_admin_circles: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: {
      content: {
        'application/json': {
          /** @example 東方愛好会 */
          name: string;
          /** @example Alice */
          author?: string;
          /** @example https://twitter.com/example */
          twitter_url?: string;
          /** @example https://pixiv.net/users/123 */
          pixiv_url?: string;
          /** @example https://example.com */
          web_url?: string;
          /** @example comic */
          category?: string;
        };
      };
    };
    responses: {
      /** @description 社团创建成功 */
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': components['schemas']['SuccessResponse'];
        };
      };
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': components['schemas']['ErrorResponse'];
        };
      };
      /** @description Conflict */
      409: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': components['schemas']['ErrorResponse'];
        };
      };
      /** @description Validation Error */
      422: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  get_admin_circles_id: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description 社团详情 */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            /** @example uuid-123 */
            id: string;
            /** @example 東方愛好会 */
            name: string;
            /** @example comic */
            category?: string | null;
            /** @example {"author":"Alice","twitter_url":"https://twitter.com/example"} */
            urls?: string | null;
            /** @example 2024-01-01T00:00:00Z */
            created_at?: string;
            /** @example 2024-01-01T00:00:00Z */
            updated_at?: string;
          };
        };
      };
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': components['schemas']['ErrorResponse'];
        };
      };
    };
  };
  put_admin_circles_id: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        'application/json': {
          name?: string;
          author?: string;
          twitter_url?: string;
          pixiv_url?: string;
          web_url?: string;
          category?: string;
        };
      };
    };
    responses: {
      /** @description 社团已保存 */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': components['schemas']['SuccessResponse'];
        };
      };
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': components['schemas']['ErrorResponse'];
        };
      };
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': components['schemas']['ErrorResponse'];
        };
      };
      /** @description Validation Error */
      422: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  delete_admin_circles_id: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description 社团已删除 */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            message: string;
          };
        };
      };
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': components['schemas']['ErrorResponse'];
        };
      };
    };
  };
  get_admin_venues: {
    parameters: {
      query?: {
        page?: string;
        pageSize?: string;
        keyword?: string;
        city?: string;
        capacity_min?: string;
        capacity_max?: string;
        has_parking?: string;
        has_wifi?: string;
      };
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description 场馆列表 */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': components['schemas']['PaginatedResult'] & {
            /** @example 120 */
            total?: number;
            /** @example 1 */
            page?: number;
            /** @example 20 */
            pageSize?: number;
            items?: {
              /** @example tokyo-big-sight */
              id: string;
              /** @example Tokyo Big Sight */
              name_en: string;
              /** @example 東京ビッグサイト */
              name_ja: string;
              /** @example 东京 Big Sight */
              name_zh: string;
              address_en?: string | null;
              address_ja?: string | null;
              address_zh?: string | null;
              /** @example 35.6298 */
              lat: number;
              /** @example 139.793 */
              lng: number;
              capacity?: number | null;
              website_url?: string | null;
              phone?: string | null;
              description_en?: string | null;
              description_ja?: string | null;
              description_zh?: string | null;
              facilities?: string | null;
              transportation?: string | null;
              parking_info?: string | null;
              created_at?: string;
              updated_at?: string;
            }[];
          };
        };
      };
    };
  };
  post_admin_venues: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: {
      content: {
        'application/json': {
          /** @example Tokyo Big Sight */
          name_en: string;
          /** @example 東京ビッグサイト */
          name_ja: string;
          /** @example 东京 Big Sight */
          name_zh: string;
          address_en?: string | null;
          address_ja?: string | null;
          address_zh?: string | null;
          /** @example 35.6298 */
          lat: number;
          /** @example 139.793 */
          lng: number;
          capacity?: number | null;
          website_url?: string | null;
          phone?: string | null;
          description_en?: string | null;
          description_ja?: string | null;
          description_zh?: string | null;
          facilities?: string | null;
          transportation?: string | null;
          parking_info?: string | null;
        };
      };
    };
    responses: {
      /** @description 创建成功 */
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            /** @example tokyo-big-sight */
            id: string;
            /** @example Tokyo Big Sight */
            name_en: string;
            /** @example 東京ビッグサイト */
            name_ja: string;
            /** @example 东京 Big Sight */
            name_zh: string;
            address_en?: string | null;
            address_ja?: string | null;
            address_zh?: string | null;
            /** @example 35.6298 */
            lat: number;
            /** @example 139.793 */
            lng: number;
            capacity?: number | null;
            website_url?: string | null;
            phone?: string | null;
            description_en?: string | null;
            description_ja?: string | null;
            description_zh?: string | null;
            facilities?: string | null;
            transportation?: string | null;
            parking_info?: string | null;
            created_at?: string;
            updated_at?: string;
          };
        };
      };
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': components['schemas']['ErrorResponse'];
        };
      };
    };
  };
  get_admin_venues_id: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description 场馆详情 */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            /** @example tokyo-big-sight */
            id: string;
            /** @example Tokyo Big Sight */
            name_en: string;
            /** @example 東京ビッグサイト */
            name_ja: string;
            /** @example 东京 Big Sight */
            name_zh: string;
            address_en?: string | null;
            address_ja?: string | null;
            address_zh?: string | null;
            /** @example 35.6298 */
            lat: number;
            /** @example 139.793 */
            lng: number;
            capacity?: number | null;
            website_url?: string | null;
            phone?: string | null;
            description_en?: string | null;
            description_ja?: string | null;
            description_zh?: string | null;
            facilities?: string | null;
            transportation?: string | null;
            parking_info?: string | null;
            created_at?: string;
            updated_at?: string;
          };
        };
      };
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': components['schemas']['ErrorResponse'];
        };
      };
    };
  };
  put_admin_venues_id: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        'application/json': {
          /** @example Tokyo Big Sight */
          name_en?: string;
          /** @example 東京ビッグサイト */
          name_ja?: string;
          /** @example 东京 Big Sight */
          name_zh?: string;
          address_en?: string | null;
          address_ja?: string | null;
          address_zh?: string | null;
          /** @example 35.6298 */
          lat?: number;
          /** @example 139.793 */
          lng?: number;
          capacity?: number | null;
          website_url?: string | null;
          phone?: string | null;
          description_en?: string | null;
          description_ja?: string | null;
          description_zh?: string | null;
          facilities?: string | null;
          transportation?: string | null;
          parking_info?: string | null;
        };
      };
    };
    responses: {
      /** @description 更新成功 */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': components['schemas']['SuccessResponse'];
        };
      };
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': components['schemas']['ErrorResponse'];
        };
      };
    };
  };
  delete_admin_venues_id: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description 删除成功 */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': components['schemas']['SuccessResponse'];
        };
      };
      /** @description 场馆正在使用中，无法删除 */
      400: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': components['schemas']['ErrorResponse'];
        };
      };
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': components['schemas']['ErrorResponse'];
        };
      };
    };
  };
  post_admin_images_upload: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: {
      content: {
        'multipart/form-data': {
          /**
           * Format: binary
           * @description 图片文件
           */
          file?: string;
          /**
           * @description 图片分类
           * @example event
           * @enum {string}
           */
          category: 'event' | 'circle' | 'venue';
          /**
           * @description 关联的资源ID
           * @example resource-uuid-789
           */
          resourceId: string;
          /**
           * @description 图片类型
           * @example poster
           * @enum {string}
           */
          imageType: 'poster' | 'logo' | 'banner' | 'gallery';
          /**
           * @description 图片变体
           * @example thumb
           * @enum {string}
           */
          variant: 'original' | 'large' | 'medium' | 'thumb';
          /**
           * @description 关联同一组图片的标识，可选
           * @example group-uuid-456
           */
          groupId?: string;
        };
      };
    };
    responses: {
      /** @description 图片上传成功 */
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': components['schemas']['SuccessResponse'];
        };
      };
      /** @description 请求参数错误 */
      400: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': components['schemas']['ErrorResponse'];
        };
      };
    };
  };
  delete_admin_images: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: {
      content: {
        'application/json': {
          /**
           * @description 要删除的图片相对路径列表
           * @example [
           *       "/images/events/reitaisai-22/poster_thumb.jpg"
           *     ]
           */
          relativePaths: string[];
        };
      };
    };
    responses: {
      /** @description 图片删除完成 */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': components['schemas']['SuccessResponse'];
        };
      };
      /** @description 请求参数错误 */
      400: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': components['schemas']['ErrorResponse'];
        };
      };
    };
  };
  get_admin_images_id: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description 图片信息 */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': components['schemas']['SuccessResponse'];
        };
      };
      /** @description 图片不存在 */
      404: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': components['schemas']['ErrorResponse'];
        };
      };
    };
  };
  get_admin_users: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description 用户列表 */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': components['schemas']['PaginatedResult'] & {
            /** @example 120 */
            total?: number;
            /** @example 1 */
            page?: number;
            /** @example 20 */
            pageSize?: number;
            items?: {
              /** @example uuid-123 */
              id: string;
              /** @example alice */
              username: string;
              /** @example viewer */
              role: string;
            }[];
          };
        };
      };
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': components['schemas']['ErrorResponse'];
        };
      };
    };
  };
  post_admin_users: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: {
      content: {
        'application/json': {
          /** @example alice */
          username: string;
          /** @example pwd12345 */
          password: string;
          /** @example viewer */
          role?: string;
        };
      };
    };
    responses: {
      /** @description 用户创建成功 */
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': components['schemas']['SuccessResponse'];
        };
      };
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': components['schemas']['ErrorResponse'];
        };
      };
      /** @description Validation Error */
      422: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  get_admin_users_id: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description 用户详情 */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            /** @example uuid-123 */
            id: string;
            /** @example alice */
            username: string;
            /** @example viewer */
            role: string;
          };
        };
      };
      /** @description Not Found */
      404: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': components['schemas']['ErrorResponse'];
        };
      };
    };
  };
  put_admin_users_id: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        'application/json': {
          /** @example alice */
          username?: string;
          /** @example editor */
          role?: string;
          /** @example newpassword */
          password?: string;
        };
      };
    };
    responses: {
      /** @description 用户信息已更新 */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': components['schemas']['SuccessResponse'];
        };
      };
      /** @description Bad Request */
      400: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': components['schemas']['ErrorResponse'];
        };
      };
      /** @description Validation Error */
      422: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  delete_admin_users_id: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description 用户已删除 */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            message: string;
          };
        };
      };
    };
  };
  get_admin_logs: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description 日志列表 */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  get_admin_stats: {
    parameters: {
      query?: {
        year?: string;
      };
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description 统计数据 */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/json': {
            totals: {
              /** @example 123 */
              circles: number;
              /** @example 456 */
              artists: number;
              /** @example 78 */
              events: number;
            };
            /** @example 2025 */
            year: number;
            eventsByMonth: {
              /** @example 01 */
              month: string;
              /** @example 12 */
              count: number;
            }[];
          };
        };
      };
    };
  };
}
