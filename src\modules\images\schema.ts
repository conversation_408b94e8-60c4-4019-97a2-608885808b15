import { z } from '@hono/zod-openapi';

// 基础 Image 对象
export const imageSchema = z.object({
  id: z.string().openapi({ example: 'uuid-123' }),
  group_id: z.string().openapi({ example: 'group-uuid-456' }),
  resource_type: z
    .enum(['event', 'circle', 'venue'])
    .openapi({ example: 'event' }),
  resource_id: z.string().openapi({ example: 'resource-uuid-789' }),
  image_type: z
    .enum(['poster', 'logo', 'banner', 'gallery'])
    .openapi({ example: 'poster' }),
  variant: z
    .enum(['original', 'large', 'medium', 'thumb'])
    .openapi({ example: 'thumb' }),
  file_path: z
    .string()
    .openapi({ example: '/images/events/reitaisai-22/poster_thumb.jpg' }),
  file_size: z.number().nullable().optional().openapi({ example: 51200 }),
  width: z.number().nullable().optional().openapi({ example: 200 }),
  height: z.number().nullable().optional().openapi({ example: 300 }),
  format: z.string().nullable().optional().openapi({ example: 'jpeg' }),
  created_at: z
    .string()
    .optional()
    .openapi({ example: '2025-01-30T00:00:00Z' }),
  updated_at: z
    .string()
    .optional()
    .openapi({ example: '2025-01-30T00:00:00Z' }),
});

export type Image = z.infer<typeof imageSchema>;

// 图片上传请求体
export const imageUploadRequest = z.object({
  category: z.enum(['event', 'circle', 'venue']).openapi({
    example: 'event',
    description: '图片分类',
  }),
  resourceId: z.string().openapi({
    example: 'resource-uuid-789',
    description: '关联的资源ID',
  }),
  imageType: z.enum(['poster', 'logo', 'banner', 'gallery']).openapi({
    example: 'poster',
    description: '图片类型',
  }),
  variant: z.enum(['original', 'large', 'medium', 'thumb']).openapi({
    example: 'thumb',
    description: '图片变体',
  }),
  groupId: z.string().optional().openapi({
    example: 'group-uuid-456',
    description: '关联同一组图片的标识，可选',
  }),
});

export type ImageUploadInput = z.infer<typeof imageUploadRequest>;

// 图片上传响应
export const imageUploadResponse = z.object({
  id: z.string().openapi({ example: 'uuid-123' }),
  groupId: z.string().openapi({ example: 'group-uuid-456' }),
  relativePath: z
    .string()
    .openapi({ example: '/images/events/reitaisai-22/poster_thumb.jpg' }),
  variant: z.string().openapi({ example: 'thumb' }),
  metadata: z.object({
    size: z.number().openapi({ example: 51200 }),
    dimensions: z.object({
      width: z.number().openapi({ example: 200 }),
      height: z.number().openapi({ example: 300 }),
    }),
    format: z.string().openapi({ example: 'jpeg' }),
  }),
});

export type ImageUploadResult = z.infer<typeof imageUploadResponse>;

// 图片列表查询参数
export const imageListQuery = z.object({
  page: z.string().optional().openapi({ example: '1' }),
  pageSize: z.string().optional().openapi({ example: '20' }),
  variant: z.enum(['original', 'large', 'medium', 'thumb']).optional().openapi({
    example: 'thumb',
    description: '按变体筛选',
  }),
  imageType: z
    .enum(['poster', 'logo', 'banner', 'gallery'])
    .optional()
    .openapi({
      example: 'poster',
      description: '按图片类型筛选',
    }),
});

export type ImageListQuery = z.infer<typeof imageListQuery>;

// 图片删除请求
export const imageDeleteRequest = z.object({
  relativePaths: z.array(z.string()).openapi({
    example: ['/images/events/reitaisai-22/poster_thumb.jpg'],
    description: '要删除的图片相对路径列表',
  }),
});

export type ImageDeleteInput = z.infer<typeof imageDeleteRequest>;

// 图片删除响应
export const imageDeleteResponse = z.object({
  deletedCount: z.number().openapi({ example: 1 }),
  failedPaths: z.array(z.string()).openapi({
    example: [],
    description: '删除失败的路径列表',
  }),
});

export type ImageDeleteResult = z.infer<typeof imageDeleteResponse>;
