import type { D1Database } from '@cloudflare/workers-types';
import {
  venueSchema,
  localizeVenue,
  type Venue,
  type LocalizedVenue,
  type VenueSearchParams,
} from './schema';

/**
 * 获取场馆列表（支持搜索和分页）
 */
export async function listVenues(
  db: D1Database,
  params: VenueSearchParams,
  locale: 'en' | 'ja' | 'zh' = 'en'
): Promise<{
  items: LocalizedVenue[];
  total: number;
  page: number;
  pageSize: number;
}> {
  const page = parseInt(params.page || '1');
  const pageSize = Math.min(parseInt(params.pageSize || '50'), 100);
  const offset = (page - 1) * pageSize;

  // 构建查询条件
  let whereClause = 'WHERE 1=1';
  const bindings: any[] = [];

  if (params.keyword) {
    whereClause += ` AND (name_en LIKE ? OR name_ja LIKE ? OR name_zh LIKE ?)`;
    const keyword = `%${params.keyword}%`;
    bindings.push(keyword, keyword, keyword);
  }

  if (params.city) {
    whereClause += ` AND (address_en LIKE ? OR address_ja LIKE ? OR address_zh LIKE ?)`;
    const city = `%${params.city}%`;
    bindings.push(city, city, city);
  }

  if (params.capacity_min) {
    whereClause += ` AND capacity >= ?`;
    bindings.push(parseInt(params.capacity_min));
  }

  if (params.capacity_max) {
    whereClause += ` AND capacity <= ?`;
    bindings.push(parseInt(params.capacity_max));
  }

  if (params.has_parking === 'true') {
    whereClause += ` AND parking_info IS NOT NULL AND parking_info != ''`;
  }

  if (params.has_wifi === 'true') {
    whereClause += ` AND facilities LIKE '%"wifi":true%'`;
  }

  // 获取总数
  const countQuery = `SELECT COUNT(*) as total FROM venues ${whereClause}`;
  const countResult = await db
    .prepare(countQuery)
    .bind(...bindings)
    .first();
  const total = countResult?.total || 0;

  // 获取数据
  const dataQuery = `
    SELECT * FROM venues 
    ${whereClause} 
    ORDER BY name_en ASC 
    LIMIT ? OFFSET ?
  `;
  const dataResult = await db
    .prepare(dataQuery)
    .bind(...bindings, pageSize, offset)
    .all();

  const venues = dataResult.results?.map((row) => venueSchema.parse(row)) || [];
  const localizedVenues = venues.map((venue) => localizeVenue(venue, locale));

  return {
    items: localizedVenues,
    total: total as number,
    page,
    pageSize,
  };
}

/**
 * 根据ID获取场馆详情
 */
export async function getVenueById(
  db: D1Database,
  id: string,
  locale: 'en' | 'ja' | 'zh' = 'en'
): Promise<LocalizedVenue | null> {
  const result = await db
    .prepare('SELECT * FROM venues WHERE id = ?')
    .bind(id)
    .first();

  if (!result) return null;

  const venue = venueSchema.parse(result);
  return localizeVenue(venue, locale);
}

/**
 * 创建新场馆
 */
export async function createVenue(
  db: D1Database,
  data: Omit<Venue, 'id' | 'created_at' | 'updated_at'> & { id: string }
): Promise<Venue> {
  const now = new Date().toISOString();
  const venue: Venue = {
    ...data,
    created_at: now,
    updated_at: now,
  };

  await db
    .prepare(
      `
      INSERT INTO venues (
        id, name_en, name_ja, name_zh,
        address_en, address_ja, address_zh,
        lat, lng, capacity, website_url, phone,
        description_en, description_ja, description_zh,
        facilities, transportation, parking_info,
        created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `
    )
    .bind(
      venue.id,
      venue.name_en,
      venue.name_ja,
      venue.name_zh,
      venue.address_en,
      venue.address_ja,
      venue.address_zh,
      venue.lat,
      venue.lng,
      venue.capacity,
      venue.website_url,
      venue.phone,
      venue.description_en,
      venue.description_ja,
      venue.description_zh,
      venue.facilities,
      venue.transportation,
      venue.parking_info,
      venue.created_at,
      venue.updated_at
    )
    .run();

  return venue;
}

/**
 * 更新场馆信息
 */
export async function updateVenue(
  db: D1Database,
  id: string,
  data: Partial<Omit<Venue, 'id' | 'created_at' | 'updated_at'>>
): Promise<void> {
  const updates: string[] = [];
  const bindings: any[] = [];

  // 动态构建更新字段
  Object.entries(data).forEach(([key, value]) => {
    if (value !== undefined) {
      updates.push(`${key} = ?`);
      bindings.push(value);
    }
  });

  if (updates.length === 0) return;

  updates.push('updated_at = ?');
  bindings.push(new Date().toISOString());
  bindings.push(id);

  const query = `UPDATE venues SET ${updates.join(', ')} WHERE id = ?`;
  await db
    .prepare(query)
    .bind(...bindings)
    .run();
}

/**
 * 删除场馆
 */
export async function deleteVenue(db: D1Database, id: string): Promise<void> {
  await db.prepare('DELETE FROM venues WHERE id = ?').bind(id).run();
}

/**
 * 检查场馆是否被events使用
 */
export async function isVenueInUse(
  db: D1Database,
  id: string
): Promise<boolean> {
  const result = await db
    .prepare('SELECT COUNT(*) as count FROM events WHERE venue_id = ?')
    .bind(id)
    .first();

  return ((result?.count as number) || 0) > 0;
}

/**
 * 获取场馆列表（管理员版本 - 返回完整多语言数据）
 */
export async function listVenuesForAdmin(
  db: D1Database,
  params: VenueSearchParams
): Promise<{
  items: Venue[];
  total: number;
  page: number;
  pageSize: number;
}> {
  const page = parseInt(params.page || '1');
  const pageSize = Math.min(parseInt(params.pageSize || '50'), 100);
  const offset = (page - 1) * pageSize;

  // 构建查询条件（复用现有逻辑）
  let whereClause = 'WHERE 1=1';
  const bindings: any[] = [];

  if (params.keyword) {
    whereClause += ` AND (name_en LIKE ? OR name_ja LIKE ? OR name_zh LIKE ?)`;
    const keyword = `%${params.keyword}%`;
    bindings.push(keyword, keyword, keyword);
  }

  if (params.city) {
    whereClause += ` AND (address_en LIKE ? OR address_ja LIKE ? OR address_zh LIKE ?)`;
    const city = `%${params.city}%`;
    bindings.push(city, city, city);
  }

  if (params.capacity_min) {
    whereClause += ` AND capacity >= ?`;
    bindings.push(parseInt(params.capacity_min));
  }

  if (params.capacity_max) {
    whereClause += ` AND capacity <= ?`;
    bindings.push(parseInt(params.capacity_max));
  }

  if (params.has_parking === 'true') {
    whereClause += ` AND parking_info IS NOT NULL AND parking_info != ''`;
  }

  if (params.has_wifi === 'true') {
    whereClause += ` AND facilities LIKE '%"wifi":true%'`;
  }

  // 获取总数
  const countQuery = `SELECT COUNT(*) as total FROM venues ${whereClause}`;
  const countResult = await db
    .prepare(countQuery)
    .bind(...bindings)
    .first();
  const total = countResult?.total || 0;

  // 获取数据
  const dataQuery = `
    SELECT * FROM venues
    ${whereClause}
    ORDER BY name_en ASC
    LIMIT ? OFFSET ?
  `;
  const dataResult = await db
    .prepare(dataQuery)
    .bind(...bindings, pageSize, offset)
    .all();

  const venues = dataResult.results?.map((row) => venueSchema.parse(row)) || [];

  return {
    items: venues, // 返回完整的Venue对象，不进行本地化
    total: total as number,
    page,
    pageSize,
  };
}

/**
 * 根据ID获取场馆详情（管理员版本）
 */
export async function getVenueByIdForAdmin(
  db: D1Database,
  id: string
): Promise<Venue | null> {
  const result = await db
    .prepare('SELECT * FROM venues WHERE id = ?')
    .bind(id)
    .first();

  if (!result) return null;

  return venueSchema.parse(result); // 返回完整的Venue对象
}

/**
 * 检查venue ID是否已存在
 */
export async function venueExists(
  db: D1Database,
  id: string
): Promise<boolean> {
  const result = await db
    .prepare('SELECT 1 FROM venues WHERE id = ? LIMIT 1')
    .bind(id)
    .first();
  return !!result;
}

/**
 * 生成唯一的venue ID
 */
export async function generateVenueId(
  db: D1Database,
  name_en: string
): Promise<string> {
  let baseId = name_en
    .toLowerCase()
    .replace(/[^a-z0-9\s]/g, '') // 移除特殊字符
    .replace(/\s+/g, '-') // 空格转连字符
    .replace(/-+/g, '-') // 多个连字符合并
    .replace(/^-|-$/g, ''); // 移除首尾连字符

  if (!baseId) {
    baseId = 'venue';
  }

  // 检查唯一性
  let id = baseId;
  let counter = 1;

  while (await venueExists(db, id)) {
    id = `${baseId}-${counter}`;
    counter++;
  }

  return id;
}
